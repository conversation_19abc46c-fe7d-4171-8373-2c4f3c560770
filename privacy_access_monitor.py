# encoding: utf-8
import argparse
import mmap
import os
import datetime
import sys
import time
from lib.common import loglog, error, pip_install_future
pip_install_future()
from past.builtins import raw_input
from lib import device_util
from lib import frida_util
import six
if six.PY2:
    from io import open


def cstr(text):
    if isinstance(text, six.text_type):
        return text
    return six.text_type(text, encoding='utf-8')


EXIT_CODE = ['q', 'exit', 'quit']
RUNTIME_LOG = "log.txt"
STACK_FILE_NAME = "stacks_privacy_api_{}.txt"
REPORT_FILE_NAME = "report_privacy_api_{}.html"

TEMPLATE_DATA_ITEM = u"""
                    <tr data-toggle="collapse" data-target=".{trname}">
                        <td>{category}</td>
                        <td><span class="d-inline-block text-truncate" style="max-width: 900px">{message}</span></td>
                        <td>{happencount}</td>
                    </tr>
"""
TEMPLATE_STACK_ITEM = u"""
<tr class="collapse {trname}">
    <td colspan=3>
        <div><pre>{stacks}</pre></div>
    </td>
</tr>
"""

_python = '"{}"'.format(sys.executable)
_adb = device_util.get_adb()
stacks_file_path = None


"""
=========== Report related =============
"""
class Stack(object):
    def __init__(self, message):
        self.parsed = False
        self.cause_api = None
        self.stacks = list()
        self.bug_type = None
        self.bug_description = None
        self.params = dict()
        self.parse(message)

    def parse(self, message):
        message = message.strip()
        lines = message.split('\n')
        self.bug_type, self.bug_description = lines[0].split(': ', 1)
        for l in lines[1:]:
            if l.startswith('   * '):
                k, v = l[5:].split(": ", 1)
                self.params[k] = v
            else:
                self.stacks.append(l)

        self.cause_api = self.stacks[0][7:]
        self.parsed = True

    def stack_string(self):
        ret = ''
        for s in self.stacks:
            ret += s + "\n"
        return ret


class Parser(object):
    def __init__(self, stacks_path):
        self.stacks_path = stacks_path
        self.stacks = list()

    def parse(self):
        if not os.path.exists(self.stacks_path):
            return
        with open(self.stacks_path, 'r', encoding='utf-8') as f:
            data = f.read()
            items = data.split('****')
            for item in items:
                text = item.strip()
                if len(text) == 0:
                    continue
                st = Stack(text)
                if st.parsed:
                    self.stacks.append(st)
            pass

    def get_type_map(self):
        type_map = dict()
        for st in self.stacks:
            if not is_problem(st):
                continue
            if st.bug_type not in type_map:
                type_map[st.bug_type] = dict()
            if st.bug_description not in type_map[st.bug_type]:
                type_map[st.bug_type][st.bug_description] = list()
            type_map[st.bug_type][st.bug_description].append(st)
        return type_map


class Report:
    TAG_ITEM_ADD = "<!-- [privacy access item add here] -->"
    TAG_DEVICE_SERIAL = "<!-- [Device Serial Here] -->"
    TAG_PACKAGE_NAME = "<!-- [Package Name Here] -->"
    TAG_APK_VERSION = "<!-- [APK Version Here] -->"
    TAG_TEST_TIME = "<!-- [Test time Here] -->"
    @staticmethod
    def generate(report_path, parser, serial, package_name, apk_version, test_time):
        template_path = os.path.join(os.path.dirname(__file__), "lib", "resource", "privacy_api_report_temp.html")
        with open(template_path, 'rb') as f:
            s = mmap.mmap(f.fileno(), 0, access=mmap.ACCESS_READ)
            s.seek(0)
            begin_at = s.find(Report.TAG_ITEM_ADD.encode('utf-8'))
            html_begin = cstr(f.read(begin_at))
            f.seek(begin_at + len(Report.TAG_ITEM_ADD.encode('utf-8')))
            html_end = cstr(f.read())

        # Replace test information
        html_begin = html_begin.replace(Report.TAG_DEVICE_SERIAL, serial)
        html_begin = html_begin.replace(Report.TAG_PACKAGE_NAME, package_name)
        html_begin = html_begin.replace(Report.TAG_APK_VERSION, apk_version)
        html_begin = html_begin.replace(Report.TAG_TEST_TIME, test_time)

        # generate html report
        but_type_map = parser.get_type_map()
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(html_begin)

            for bug_type, data in but_type_map.items():
                if data is None:
                    continue

                cnt = 1;
                for bug_description, st_list in data.items():
                    import hashlib
                    bug_hash = int(hashlib.sha256(bug_type.encode('utf-8')).hexdigest(), 16) % 10**8
                    trname = 'h' + str(bug_hash) + "-" + str(cnt)

                    stack_str = ''
                    happen = 0
                    for st in st_list:
                        happen += 1
                        stack_str += "{}. {}\n".format(happen, st.params.get('Time'))
                        stack_str += "Process: {}\n".format(st.params.get('Process'))
                        stack_str += st.stack_string() + "\n\n"
                    date_html = TEMPLATE_DATA_ITEM.format(trname=trname, category=bug_type,
                                                          message=bug_description, happencount=len(st_list))
                    stack_html = TEMPLATE_STACK_ITEM.format(trname=trname, stacks=stack_str)

                    f.write(date_html)
                    f.write(stack_html)
                    cnt += 1

            f.write(html_end)


def is_problem(stack):
    if 'com.qualcomm.qti.Performance.checkAppPlatformSigned' in stack.stacks[0]:
        return False
    return True


def advance_mode(arguments):
    print('')
    print('进阶配置: 请输入您要设置的功能 Key')
    print('Advance Mode: Please input the key of feature')
    print('Key        | Feature')
    print('---------------------------------')
    print('activity   | 指定启动页面 Specific launch activity page.')
    print('no-clear   | 测试前不清除资料 Do not clear data before test.')
    print('manual     | 不重启进程，使用此功能时需先手动启动应用。Do not re-start process. Please make sure the'
          ' application have launched before test.')
    print('q          | 离开进阶设置 Quit advance setup.')
    print('')

    while True:
        ch = raw_input('Enter your key:')
        if not ch:
            continue
        ch = ch.strip().lower()

        if ch == 'q':
            return
        elif ch == 'activity':
            print('')
            print('输入页面名称 Input your activity name. (Ex: .MainActivity)')
            input_val = raw_input()
            if input_val:
                arguments.activity = input_val
                print('Activity set to {}'.format(input_val))
                print('')
        elif ch == 'no-clear':
            arguments.no_clear = True
            print('Set no-clear to True.')
            print('')
        elif ch == 'manual':
            arguments.manual = True
            print('Set manual to True.')
            print('')
        else:
            print('Unknown key: {}'.format(ch))
            print('')


def _on_message_handle(message, data):
    if message['type'] == 'send':
        global stacks_file_path
        dt_str = datetime.datetime.fromtimestamp(int(message['payload']['time'])/1000).strftime("%Y/%m/%d %H:%M:%S")
        with open(stacks_file_path, 'a', encoding="utf-8") as f:
            f.write(u'**** {}\n'.format(message['payload'].get('summary')))
            f.write(u'   * Time: {}\n'.format(dt_str))
            f.write(u'   * Process: {}\n'.format(message['payload'].get('process')))
            f.write(message['payload'].get('stacks'))
    else:
        print(message, data)


def main():
    frida_util.install_frida_client()
    loglog(_python, printScreen=True)
    print('')
    loglog('Script version: V0.0.00.17', printScreen=True)

    description = """
*********************************************************************
**                        隐私信息收集监控                           **
**               Privacy information access monitor                **
**  https://xiaomi.f.mioffice.cn/docs/dock4jszc6L1ulvjTy8aY3JqPO5  **
*********************************************************************
    也可以使用指令列直接运行
    You can input the parameters in the Runtime or set it in command line.
Help:
    py {script} -h
Step by step: 
    py {script}
Command line: 
    py {script} [-s SERIAL] [-p PACKAGE] [-a ACTIVITY] [-nc] [-m]
    """.format(script=os.path.basename(__file__))
    parser = argparse.ArgumentParser(description=description, formatter_class=argparse.RawTextHelpFormatter)
    parser.add_argument("-s", "--serial", type=str, help="device serial.")
    parser.add_argument("-p", "--package", type=str, help="Testing application package name")
    parser.add_argument("-o", "--output", type=str, help="Report output folder path.")
    parser.add_argument("-a", "--activity", type=str, help="Specific testing activity name. For example: .MainActivity")
    parser.add_argument("-nc", "--no-clear", action="store_true", help="Do not clear application data before test")
    parser.add_argument("-m", "--manual",action="store_true", help="Launch the testing application manually. \n"
                                                                   "Script will not auto re-start the activity for you. \n"
                                                                   "Please make sure the process is killed before test.\n"
                                                                   "You can use this featre to test the application which do not have default activity. ex: Launcher, Updater")
    args = parser.parse_args()
    parser.print_help()

    setattr(args, 'timestr', datetime.datetime.now().strftime("%Y%m%d_%H%M"))
    test_time = datetime.datetime.now().strftime("%Y/%m/%d %H:%M")
    if not (args.serial and args.package):
        if not args.serial:
            args.serial = device_util.get_device()

        ch = ''
        while ch == '' or len(ch) < 5:
            print('')
            print('请输入包名')
            print('Please input package name:')
            ch = raw_input()
        args.package = ch.strip()

        print('')
        print('请输入要生成的报告文件夹路径。直接回车默认為当前脚本路径。')
        print('Please input report folder path: (default=current folder)')
        ch = raw_input()
        args.output = ch.strip()

        print('')
        print('进入进阶配置。 输入(Y)/(Yes)进入进阶配置。直接回车可跳过。')
        print('Type (Y)/(Yes) to enter advance mode. Press Enter to skip.')
        ch = raw_input()
        if ch and ch.lower() == 'y' or ch.lower() == 'yes':
            advance_mode(args)

    if not args.output:
        args.output = os.path.join(os.path.dirname(__file__), 'report')
    else:
        args.output = os.path.abspath(args.output)
    if not os.path.exists(args.output):
        os.makedirs(args.output)

    loglog('*********************************', printScreen=True)
    loglog('* Serial = [%s]' % args.serial, printScreen=True)
    loglog('* Package Name = [%s]' % args.package, printScreen=True)
    loglog('* Report path = [%s]' % args.output, printScreen=True)
    if args.activity:
        loglog('* Activity = [%s]' % args.activity, printScreen=True)
    if args.no_clear:
        loglog('* [Not clear data]', printScreen=True)
    if args.manual:
        loglog('* [Manual launch app]', printScreen=True)
    loglog('*********************************', printScreen=True)

    device_daemon = None
    monitor_thread = None
    apk_version = device_util.get_apk_version(args.serial, args.package)
    try:
        # Make sure device exists
        print('')
        print('等待装置 %s 连接' % args.serial)
        print('Waiting for device %s ready' % args.serial)
        device_util.wait_for_device_ready(args.serial)

        if not args.no_clear:
            print('')
            print('清除应用资料')
            print('Clearing application data...')
            ret, err = device_util.clear_data(args.serial, args.package)
            loglog(ret, err)
        print('')
        print('建立环境，请稍后')
        print('Starting service, please wait...')
        # Install frida server to device.
        frida_util.install_frida_server(args.serial)
        # Start frida server on devie.
        device_daemon = frida_util.FridaDeviceThread(args.serial)
        device_daemon.start()
        device_daemon.wait_for_started()
        # Start Frida client to access device frida server
        global stacks_file_path
        file_postfix = "{pkg}_{time}".format(pkg=args.package, time=args.timestr)
        stacks_file_path = os.path.join(args.output, STACK_FILE_NAME.format(file_postfix))
        privacy_js = os.path.join(os.path.dirname(__file__), 'lib', 'resource', 'privacy.js')
        monitor_thread = frida_util.FridaThread(args, _on_message_handle, privacy_js)
        monitor_thread.start()
        time.sleep(2)  # wait 2 seconds for command execute
        if monitor_thread.error_msg is not None:
            monitor_thread.stop()
            error('执行脚本失败. Fail to execute script.', monitor_thread.error_msg)
        print('')
        print('========================')
        print('===    环境准备完成    ===')
        print('===   Ready to test  ===')
        print('========================')
        print('')
        print('请开始执行你的测试场景')
        print('You can do your test scenario now.')
        print('')
        print('当结束测试后，请输入"q"离开')
        ch = ''
        while ch.lower() not in EXIT_CODE:
            print('')
            ch = raw_input('Enter "q" to finish capture.')
        print('')
        print('结束中...')
        print('Stopping....')
    finally:
        if monitor_thread:
            monitor_thread.stop()
        time.sleep(1)
        if device_daemon:
            device_daemon.stop()

    # Capture finish, generate reports
    print('')
    print('抓取结束')
    print('Stop Capture')

    if monitor_thread.hook_process_count() == 0:
        error('测试过程中无法找到任何进程', 'Cannot find any process during test')

    # Parse result and generate report
    if monitor_thread is not None:
        parser = Parser(stacks_file_path)
        parser.parse()
        file_postfix = "{pkg}_{time}".format(pkg=args.package, time=args.timestr)
        report_html_path = os.path.join(args.output, REPORT_FILE_NAME.format(file_postfix))
        Report.generate(report_html_path, parser, args.serial, args.package, apk_version, test_time)

    print('')
    print('报告已储存到 %s' % report_html_path)
    print('Report save to : %s' % report_html_path)

    raw_input('Press Enter to exit.')


"""
Input required information and script will capture application privacy access.
EX: 
py privacy_access_monitor -s 2ac0c44a -p "com.mi.globalbrowser" -o "/home/<USER>/work/frida"
"""
if __name__ == '__main__':
    main()
