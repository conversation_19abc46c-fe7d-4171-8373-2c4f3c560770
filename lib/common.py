# encoding: utf-8
import datetime
import os
import sys

RUNTIME_LOG = "log.txt"
_python = '"{}"'.format(sys.executable)


def download(url, folder, filename=None, quite=False):
    if not filename:
        filename = os.path.basename(url)
    path = os.path.join(folder, filename)
    if not os.path.exists(path):
        if not quite:
            print('Download %s' % url)
        import six
        if six.PY2:
            from urllib import urlretrieve
        else:
            from urllib.request import urlretrieve
        urlretrieve(url, path)


def loglog(message, printScreen=False):
    if printScreen:
        print(message)
    message = str(message)
    time_str = datetime.datetime.now().strftime("%m/%d/%Y %H:%M:%S ")
    args = {'encoding': "utf-8"} if sys.version_info[0] >= 3 else {}
    with open(RUNTIME_LOG, 'a', **args) as f:
        f.write(time_str)
        f.write(message)
        f.write('\n')


def error(msg_cn, msg_en):
    from past.builtins import raw_input
    print('')
    if msg_cn:
        print(msg_cn)
    if msg_en:
        print(msg_en)
    raw_input('Press Enter to exit.')
    exit(1)


def pip_install_future():
    if pip_not_installed('future'):
        pip_install('future')
    if pip_not_installed('six'):
        pip_install('six')


def pip_not_installed(pkgs):
    if isinstance(pkgs, str):
        pkgs = [pkgs]

    not_installed = list()
    cmd = '{} -m pip list'.format(_python)
    import subprocess
    ret, err = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE).communicate()
    ret = ret.decode('utf-8')
    for pkg in pkgs:
        if pkg.split('==')[0] not in ret:
            not_installed.append(pkg)
    return not_installed


def pip_install(pkgs):
    print('')
    print('安装必要库')
    print('Installing library')
    if isinstance(pkgs, str):
        pkgs = [pkgs]
    for pkg in pkgs:
        os.system('{py} -m pip install {pkg}'.format(py=_python, pkg=pkg))
    print('')
    print('')


if __name__ == '__main__':
    pip_install_future()
