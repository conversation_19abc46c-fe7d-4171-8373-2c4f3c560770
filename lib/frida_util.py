# encoding: utf-8
import datetime
import os
import re
import subprocess
import sys
import threading
import time
from lib import device_util
from lib.common import loglog
import six
if six.PY2:
    from io import open

_python = '"{}"'.format(sys.executable)
_adb = device_util.get_adb()

print('user adb: {}'.format(_adb))
print('user python: {}'.format(_python))


class FridaClient(object):
    def __init__(self, arguments, on_mesage_handle, js_file):
        self.arguments = arguments
        self.serial = arguments.serial
        self.package_name = arguments.package
        with open(js_file, 'rb') as f:
            self.js_code = 'var test_package="{}";\n'.format(self.package_name) + f.read().decode('utf-8')
        self.on_mesage_handle = on_mesage_handle
        self.device = None
        self.error_msg = None
        self.process_name = None
        self._on_message_func = None
        self._add_spawn_func = None
        self.scripts = list()
        self.hook_process_count = 0

    def monitor(self):
        def _spawn_added(spawn):
            if spawn.identifier and (
                    (self.process_name and self.process_name in spawn.identifier)
                    or (self.package_name in spawn.identifier)
            ):
                self.hook_process_count += 1
                print('')
                print('[*] Attach Process %s(%s)' % (spawn.identifier, spawn.pid))
                session = self.device.attach(spawn.pid)
                script = session.create_script(self.js_code)
                script.on('message', self.on_mesage_handle)
                script.load()
                self.scripts.append(script)
                time.sleep(1)
                print('[*] Resume Process %s(%s)' % (spawn.identifier, spawn.pid))
            self.device.resume(spawn.pid)

        self._add_spawn_func = _spawn_added
        import frida
        try:
            self.device = frida.get_device(self.serial)
            self.device.on('spawn-added', _spawn_added)
            self.device.enable_spawn_gating()
            kwargs = dict()
            spawn_create = False
            if not self.arguments.manual:
                if self.arguments.activity:
                    kwargs = {'activity': self.arguments.activity}
                pid = self.device.spawn(self.package_name, **kwargs)
                spawn_create = True
            else:
                pid = self.get_pid(self.arguments.package)

            if pid:
                pross = self.device.enumerate_processes()
                for p in pross:
                    if p.pid == pid:
                        self.process_name = p.name
                        break
                session = self.device.attach(pid)
                script = session.create_script(self.js_code)
                script.on('message', self.on_mesage_handle)
                print('[*] Running CTF')
                script.load()
                self.scripts.append(script)
                print("[*] Attach Main Process %s(%s)" % (self.process_name, pid))
                self.hook_process_count += 1
            if spawn_create:
                self.device.resume(pid)

        except Exception as e:
            print('Exception Happen:')
            loglog(e, printScreen=True)
            self.error_msg = str(e)

    def stop_monitor(self):
        loglog('FridaClient.stop_monitor()')
        if self.device:
            self.device.disable_spawn_gating()
            self.device.off('spawn-added', self._add_spawn_func)
        for s in self.scripts:
            s.off('message', self.on_mesage_handle)

    def wait_for_process(self, package_name):
        pid = None
        retry = 5
        while not pid:
            retry -= 1
            if retry < 0:
                break
            else:
                print('Wait for pid....')
                time.sleep(10)
            pid = self.get_pid(package_name)
            print('pid = ',pid)
        return pid

    def get_pid(self, package_name):
        adb_cmd = "{adb} -s {s} shell \"ps -A | grep '{pkg}'\"".format(adb=_adb, s=self.serial, pkg=package_name)
        ret, err = subprocess.Popen(adb_cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE).communicate()
        if ret:
            matcher = re.match('[^ ]*\s+(\d+)\s+\d+\s+\d+\s+\d+\s+.*', ret.decode('ascii'))
            if matcher:
                return int(matcher.group(1))
        return None


class FridaDeviceThread(threading.Thread):
    def __init__(self, serial):
        super(FridaDeviceThread, self).__init__()
        self.serial = serial
        self._stop_monitor = False
        self.monitor_proc = None
        self._event = threading.Event()

    def run(self):
        loglog('FridaDeviceThread:: run() >> ')
        self.kill_frida_server(restart=True)
        cmds = [_adb, '-s', self.serial, 'shell', '/data/local/tmp/frida-server &']
        self.monitor_proc = subprocess.Popen(cmds, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, stdin=subprocess.PIPE)
        # Wait for device frida server ready
        time.sleep(2)
        self._event.set()
        loglog('FridaDeviceThread:: run() << ')

    def wait_for_started(self):
        loglog('FridaDeviceThread:: wait_for_finish() >> ')
        self._event.wait()
        loglog('FridaDeviceThread:: wait_for_finish() << ')
        pass

    def stop(self):
        loglog('FridaDeviceThread.stop()')
        self._stop_monitor = True
        if self.monitor_proc is not None and self.monitor_proc.poll() is None:
            self.monitor_proc.kill()
        self.monitor_proc = None
        self.kill_frida_server()

    def kill_frida_server(self, restart=False):
        adb_cmd = "{adb} -s {s} shell \"ps -A | grep 'frida-server'\"".format(adb=_adb, s=self.serial)
        ret, err = subprocess.Popen(adb_cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE).communicate()
        if b'frida-server' in ret:
            matcher = re.match('^([a-zA-Z_\d]+)\s+(\d{3,})\s+.*$', ret.decode('utf-8'))
            if matcher:
                adb_cmd = "{adb} -s {s} shell kill {pid}".format(adb=_adb, s=self.serial, pid=matcher.group(2))
                ret, err = subprocess.Popen(adb_cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE).communicate()
                if ret:
                    print(ret)
                if err:
                    print(err)
                if restart:
                    print('Restart frida-server %s' % matcher.group(2))
                else:
                    print('killed frida-server %s' % matcher.group(2))


class FridaThread(threading.Thread):
    def __init__(self, arguments, on_message_handle, javascript_path, thread_name=None):
        super(FridaThread, self).__init__(name=thread_name)
        loglog('MonitorThread init(%s)' % self.name)
        threading.Thread.__init__(self)
        self.serial = arguments.serial
        self.package_name = arguments.package
        self.output_folder = arguments.output
        self.arguments = arguments
        self.running = False
        self._stop_monitor = False
        self.monitor_proc = None
        self.error_msg = None
        self.on_message_handle = on_message_handle
        self.frida_client = None
        self.javascript_path = javascript_path

    def run(self):
        loglog('FridaThread:: start_monitor() >> ')
        self.running = True

        self.frida_client = FridaClient(self.arguments, self.on_message_handle, self.javascript_path)
        self.frida_client.monitor()
        self.error_msg = self.frida_client.error_msg
        # while self.error_msg is None:
        #     if self._stop_monitor:
        #         loglog('FridaThread.run(): monitor stop.')
        #         break
        #     time.sleep(2)
        loglog('FridaThread:: start_monitor() << ')

    def stop(self):
        loglog('FridaThread.stop()')
        self._stop_monitor = True
        if self.frida_client:
            self.frida_client.stop_monitor()
        if self.monitor_proc is not None:
            loglog('FridaThread.stop(): send exit to client')
            while self.monitor_proc.poll() is None:
                try:
                    self.monitor_proc.communicate(input=b'\nexit\n')
                except Exception as e:
                    print(e)
            loglog('FridaThread.stop(): exit finish')
            self.monitor_proc = None

    def hook_process_count(self):
        return self.frida_client.hook_process_count


def install_frida_server(serial):
    adb_cmd = "{adb} -s {s} shell ls /data/local/tmp".format(adb=_adb, s=serial)
    ret, err = subprocess.Popen(adb_cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE).communicate()
    if b'frida-server' in ret:
        return
    adb_cmd = "{adb} -s {s} root".format(adb=_adb, s=serial)
    os.system(adb_cmd)
    from lib import common
    resource_path = os.path.abspath(os.path.join(os.path.dirname(__file__), 'resource'))
    common.download('http://cnbj1-fds.api.xiaomi.net/global.ci/others/frida/frida-server', resource_path)
    adb_cmd = "{adb} -s {s} push \"{frida_server}\" /data/local/tmp".format(adb=_adb, s=serial,
                                                                            frida_server=os.path.join(resource_path, 'frida-server'))
    os.system(adb_cmd)
    adb_cmd = "{adb} -s {s} shell chmod +x /data/local/tmp/frida-server".format(adb=_adb, s=serial)
    os.system(adb_cmd)


def install_frida_client():
    from lib import common
    if common.pip_not_installed('frida-tools'):
        common.pip_install('frida-tools==10.2.1')


def get_apk_version(serial, package_name):
    adb_cmd = '{adb} -s {s} shell "dumpsys package {pkg} | grep versionName"'.format(adb=_adb, s=serial, pkg=package_name)
    ret, err = subprocess.Popen(adb_cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE).communicate()
    tmp = ret.decode('utf-8').split('versionName=')
    if len(tmp) <= 1:
        return 'Unknown'
    else:
        return tmp[1].strip()


def check_device_available(serial):
    adb_cmd = "{adb} -s {s} get-state".format(adb=_adb, s=serial)
    std_result, std_error = subprocess.Popen(adb_cmd, shell=True, stdout=subprocess.PIPE,
                                             stderr=subprocess.PIPE).communicate()
    exist = b"device" in std_result
    if exist:
        print('')
        print('Root device')
        os.system('{adb} -s {s} root'.format(adb=_adb, s=serial))
    else:
        print('')
        loglog('无法找到装置. Cannot found device {s}'.format(s=serial), printScreen=True)
        loglog('{cmd}'.format(cmd=adb_cmd), printScreen=True)
        if std_result:
            loglog('adb std: {err}'.format(err=std_error), printScreen=True)
        if std_error:
            loglog('err: {err}'.format(err=std_error), printScreen=True)
    return exist


def clear_data(serial, package_name):
    adb_cmd = "{adb} -s {s} shell pm clear {pkg}".format(adb=_adb, s=serial, pkg=package_name)
    loglog(subprocess.Popen(adb_cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE).communicate())
