var metadata={
  "name": "privacy total",
  "time": "20210823"
}

var Throwable = null;
Java.perform(function () {
  Throwable = Java.use("java.lang.Throwable");
});
function PrintStack(summary) {
  var JavaDate = Java.use("java.util.Date");
  var stackElements = Throwable.$new().getStackTrace();
  var body = "";
  var length = stackElements.length;
  for (var i = 0; i < length; i++) {
    body += "    at " + stackElements[i] + "\n";
  }
  // console.log("\n\n****" + summary)
  // console.log(body);

  var Application = Java.use('android.app.Application');
  var processName = Application.getProcessName.apply(Application)
  var message = {
    summary: summary,
    stacks: body,
    process: processName,
    pid: Process.id,
    tid: Process.getCurrentThreadId(),
    time: JavaDate.$new().getTime()
  }
  send(message);
};

function loglog(msg){
  console.log(msg)
}

function issamemethod(m, params){
  if(!m) return true;
  var tmp = "";
  m.argumentTypes.forEach(p=>{
    tmp += "'"+p.className+"', "
  })

  if(tmp == params+', '){
    return true
  }
  return false
}

function hook(apis) {
  var Exception = Java.use('java.lang.Exception');
  apis.forEach(api => {
    var toHook;
    try {
      var clazz = api.class;
      var method = api.method;
      var name = api.name;
      //callback，接收函数参数及函数返回值，并返回结果（'noout'不输出）
      var callback = api.callback;
      var params = api.params;
      try {
        if (api.target && parseInt(Java.androidVersion, 10) < api.target) {
          loglog('[API Monitor] Not Hooking unavailable class/method - ' + clazz + '.' + method)
          return
        }
        // Check if class and method is available
        toHook = Java.use(clazz)[method];
        if (!toHook) {
          loglog('[API Monitor] Cannot find ' + clazz + '.' + method);
          return
        }
      } catch (err) {
        loglog('[API Monitor] Cannot find ' + clazz + '.' + method);
        return
      }

      var overloadCount = toHook.overloads.length;
      for (var i = 0; i < overloadCount; i++) {
        //区分重载函数，避免重复hook
        if(params && !issamemethod(toHook.overloads[i], params)){continue;}
        toHook.overloads[i].implementation = function () {
          var argz = [].slice.call(arguments);
          var retval = this[method].apply(this, arguments);
          //var calledFrom = Exception.$new().getStackTrace().toString().split(',')[1];
          var callstack = Exception.$new().getStackTrace().toString();
          var ret = retval? retval.toString() : "";
          if(callback){
            ret = callback(retval, arguments)
            if ((typeof ret) != 'string') {
              if (ret['name'] != null) {
                name = ret['name']
              }
              ret = ret['result']
            }
          }
          var message = {
            name: name,
            class: clazz,
            method: method,
            arguments: argz,
            result: ret,
            callstack: callstack
          };
          if(ret != 'noout'){
            //send(message)
            PrintStack(message.name+': '+ret)
          }

          return retval;
        }
      }
    }catch (err) {
      loglog('[API Monitor] - ERROR: ' + clazz + "." + method + " [\"Error\"] => " + err);
    }

  });

}

//未经同意收集imei/mac/app info or list/sdcard
function hookInstallPackage() {
  hook([
    {
      'class': 'android.app.ApplicationPackageManager',
      'method': 'getInstalledApplications',
      'name': '获取应用列表',
      'target': '6',
      'callback': function(ret){return ret.size()},
    },
    {
      'class': 'android.app.ApplicationPackageManager',
      'method': 'getInstalledPackages',
      'name': '获取应用列表pkg',
      'target': '6',
      'callback': function(ret){return ret.size()},
    }
    // ,
    // {
    //   'class': 'android.app.ApplicationPackageManager',
    //   'method': 'getPackageInfo',
    //   'name': '获取应用信息',
    //   'target': '6',
    //   'callback': function(ret, args){return args[0]},
    // }
  ])
}

function toHexString(byteArray) {
  if(!byteArray)return null
  return Array.from(byteArray, function(byte) {
    return ('0' + (byte & 0xFF).toString(16)).slice(-2);
  }).join('')
}
function hookWifiPart() {
  hook([
    {
      'class': 'android.net.wifi.WifiInfo',
      'method': 'getMacAddress',
      'name': '获取WiFi Mac',
      'target': '6'
    },
    {
      'class': 'android.net.wifi.WifiInfo',
      'method': 'getSSID',
      'name': '获取WiFi名称',
      'target': '6'
    },
    {
      'class': 'android.net.wifi.WifiInfo',
      'method': 'getBSSID',
      'name': '获取WiFi Mac(bssid)',
      'target': '6'
    },
    {
      'class': 'android.net.wifi.WifiInfo',
      'method': 'getIpAddress',
      'name': '获取WiFi IP',
      'target': '6',
      'callback': function(ret){return (ret & 0xff)+'.'+(ret>>8 & 0xff)+'.'+(ret>>16 & 0xff)+'.'+(ret>>24 & 0xff);}
    },
    {
      'class': 'java.net.NetworkInterface',
      'method': 'getHardwareAddress',
      'name': 'Java获取Mac',
      'target': '6',
      'callback': function(ret){return toHexString(ret)}
    },
    {
      'class': 'android.net.wifi.WifiManager',
      'method': 'getConfiguredNetworks',
      'name': '获取保存的WiFi',
      'target': '6',
      'callback': function(ret){return ret.size()}
    }
  ])
}

function phoneid() {
  hook([
    {//(硬件关联不可变)
      'class': 'com.android.id.impl.IdProviderImpl',
      'method': 'getUDID',
      'name': '获取手机标识UDID',
      'target': '6'
    },
    {//(重置后变化)
      'class': 'com.android.id.impl.IdProviderImpl',
      'method': 'getOAID',
      'name': '获取OAID',
      'target': '6'
    },
    {//(开发者关联)
      'class': 'com.android.id.impl.IdProviderImpl',
      'method': 'getVAID',
      'name': '获取VAID',
      'target': '6'
    },
    {//(APP关联)
      'class': 'com.android.id.impl.IdProviderImpl',
      'method': 'getAAID',
      'name': '获取getAAID',
      'target': '6'
    },
    {
      'class': 'android.provider.Settings$Secure',
      'method': 'getString',
      'name': '获取android_id(8.0+，同VAID)',
      'target': '6',
      'callback': function(ret, args){if(args[1] == 'android_id'){return 'android_id:'+ret} else{return 'noout'}}
    }
  ])
}

function telephonyid() {
  hook([
    {
      'class': 'android.telephony.TelephonyManager',
      'method': 'getCallState',
      'name': '获取呼叫状态',
      'target': '6'
    },
    {
      'class': 'android.telephony.TelephonyManager',
      'method': 'getImei',
      'name': '获取Imei',
      'target': '6'
    },
    {
      'class': 'android.telephony.TelephonyManager',
      'method': 'getMeid',
      'name': '获取Meid',
      'target': '6'
    },
    {
      'class': 'android.telephony.TelephonyManager',
      'method': 'getSubscriberId',
      'name': '获取imsi',
      'target': '6'
    },
    {
      'class': 'android.telephony.TelephonyManager',
      'method': 'getLine1Number',
      'name': '获取手机号',
      'target': '6'
    },
    {
      'class': 'android.telephony.TelephonyManager',
      'method': 'getDeviceId',
      'name': '获取sim卡标识(imei/meid)',
      'target': '6'
    }
  ])
}

function sdcard(){
  //同意隐私政策前 sdcard读写
  hook([
    {
      'class': 'java.io.FileInputStream',
      'method': '$init',
      'name': '读SD卡存储',
      'target': '6',
      'params': "'java.io.File'",
      'callback': function(ret, args){
        if(args[0].getPath().startsWith('/storage/emulated/0/')){
            if (!args[0].getPath().startsWith('/storage/emulated/0/Android/data/'+test_package)) {
              return args[0].getPath()
            }
        } else if (args[0].getPath() == '/proc/cpuinfo') {
          return {'name': '查询CPU信息', 'result': args[0].getPath()}
        } else if (args[0].getPath() == '/sys/class/net/wlan0/address') {
          return {'name': '查询MAC信息', 'result': args[0].getPath()}
        }
        return 'noout';
      }
    },
    {
      'class': 'java.io.FileOutputStream',
      'method': '$init',
      'name': '写SD卡存储',
      'target': '6',
      'params': "'java.io.File', 'boolean'",
      'callback': function(ret, args){
        if(args[0].getPath().startsWith('/storage/emulated/0/')){
          if (!args[0].getPath().startsWith('/storage/emulated/0/Android/data/'+test_package)) {
            return args[0].getPath()
          }
        }
        return 'noout';
      }
    },
    {
      'class': 'java.io.RandomAccessFile',
      'method': '$init',
      'name': '写SD卡存储(同意隐私政策前?)',
      'target': '6',
      'params': "'java.io.File', 'boolean'",
      'callback': function(ret, args){
        if(args[0].getPath().startsWith('/storage/emulated/0/')){
          return args[0].getPath()
        }
        return 'noout';
      }
    }
  ])
}

function hookGPS() {
  //会有权限弹框
  hook(
      [{
        'class': 'android.location.LocationManager',
        'method': 'requestLocationUpdates',
        'name': '获取gps实时位置',
        'target': '6'
      },
        {
          'class': 'android.location.LocationManager',
          'method': 'getLastKnownLocation',
          'name': '获取gps位置',
          'target': '6'
        }]
  )

}

function runningtasks(){
  //系统应用才能使用
  hook([
    {
      'class': 'android.app.ActivityManager',
      'method': 'getRunningTasks',
      'name': '获取运行中的任务',
      'target': '6',
      'callback': function(ret){return ret.size()},
    },
    {
      'class': 'android.app.ActivityManager',
      'method': 'getRecentTasks',
      'name': '获取最近任务',
      'target': '6',
      'callback': function(ret){return ret.size()},
    },
    {
      'class': 'android.app.ActivityManager',
      'method': 'getRunningAppProcesses',
      'callback': function(ret){return ret.size()},
      'name': '获取运行中的APP进程',
      'target': '6'
    }
  ])

}
function autostart(){
  var AUTO_START_ACTIONS = [
    'android.intent.action.BOOT_COMPLETED',
    'android.intent.action.BATTERY_CHANGED',
    'android.bluetooth.device.action.NAME_CHANGED',
    'android.intent.action.MEDIA_MOUNTED',
    'android.intent.action.USER_PRESENT',
    'android.intent.action.SCREEN_ON',
    'android.intent.action.PACKAGE_ADDED',
    'android.intent.action.CLOSE_SYSTEM_DIALOGS',
    'android.intent.action.AIRPLANE_MODE'
  ]
  //监听系统广播等自启动或关联启动
  hook([
    {
      'class': 'android.content.ContextWrapper',
      'method': 'registerReceiver',
      'name': '注册的广播action',
      'target': '6',
      'callback': function(ret, args){
        var autostart = ''
        var sms_receiver = ''
        for(var i=0;i<args[1].countActions();i++){
          let t = args[1].getAction(i);
          if (AUTO_START_ACTIONS.includes(t)) {
            autostart += (autostart?'|':'') + t;
          }
          if (t == 'android.provider.Telephony.SMS_RECEIVED') {
            sms_receiver = t
          }
        }
        if (autostart) {
          return {'name': '注册自启动Action', 'result': autostart}
        }
        if (sms_receiver) {
          return {'name': '注册监听SMS', 'result': sms_receiver}
        }
        return 'noout'
      }
    }
  ])
}

function getsensor(){
  //获取传感器
  hook([
    {
      'class': 'android.hardware.SensorManager',
      'method': 'getDefaultSensor',
      'name': '读取传感器',
      'target': '6',
    }
  ])
}

function contentProvider(){
  hook([
    {
      'class': 'android.content.ContentResolver',
      'method': 'query',
      'name': '查询数据(ContentProvider)',
      'target': '6',
      'callback': function(ret, args){
        var out = 'noout'
        var uri = args[0].toString()
        if (uri.startsWith('content://sms')) {
          out = {'name': '查询SMS', 'result': uri}
        } else if (uri.startsWith('content://com.android.contacts')) {
          out = {'name': '查询联络人', 'result': uri}
        } else if (uri.startsWith('content://com.android.calendar')) {
          out = {'name': '查询Calendar', 'result': uri}
        } else if (uri.startsWith('content://call_log')) {
          out = {'name': '查询CallLog', 'result': uri}
        }
        return out
      },
      'params': "'android.net.Uri', '[Ljava.lang.String;', 'android.os.Bundle', 'android.os.CancellationSignal'"
    }
  ])
}

function accounts() {
  hook([
    {
      'class': 'android.accounts.AccountManager',
      'method': 'getAccounts',
      'name': '读取帐户信息',
      'target': '6',
    }
  ])
}

function hookBt() {
  hook([
    {
      'class': 'android.bluetooth.BluetoothAdapter',
      'method': 'getAddress',
      'name': '读取蓝牙MAC',
      'target': '6',
    }
  ])
}

function runtimeexec(){
  hook([
    {
      'class': 'java.lang.Runtime',
      'method': 'exec',
      'name': '调用系统呼叫',
      'target': '6',
      'callback': function(ret, args){
        var as = [].slice.call(args);as = as.join()
        if(as.indexOf('/proc/cpuinfo') > -1){
          return {'name': '查询cpu序列号', 'result': as};
        }
        if(as.indexOf('/sys/class/net/') > -1){
          return {'name': '查询物理MAC', 'result': as};
        }
        return 'noout'}
    }
  ])
}
function serialnum(){
  hook([
    {
      'class': 'android.os.Build',
      'method': 'getSerial',
      'name': '查询设备序列号',
      'target': '6'
    }
  ])
}

Java.perform(function() {
  hookGPS();
  hookWifiPart();
  hookInstallPackage();
  phoneid();
  telephonyid();
  sdcard()
  runningtasks()
  autostart()
  getsensor()
  contentProvider()
  accounts()
  hookBt()
  runtimeexec()
  serialnum()
  // hook(
  //   {
  //     'class': 'java.lang.Runtime',
  //     'method': 'exec'
  //   }
  // )

});
