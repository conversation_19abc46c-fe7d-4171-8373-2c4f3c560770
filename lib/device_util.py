# encoding: utf-8
import os
import re
import subprocess

_adb = None


def get_adb():
    global _adb
    if _adb is None:
        ret, err = subprocess.Popen('adb --version', shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE).communicate()
        if 'Android' in ret.decode('utf-8'):
            _adb = 'adb'
        else:
            bin_folder = os.path.abspath(os.path.join(os.path.dirname(__file__), 'resource'))
            import sys, stat
            from lib import common
            print('OS: %s' % sys.platform)
            if sys.platform == "win32" or sys.platform == "win64":
                common.download('http://cnbj1-fds.api.xiaomi.net/global.ci/others/adb/win/AdbWinApi.dll', bin_folder)
                common.download('http://cnbj1-fds.api.xiaomi.net/global.ci/others/adb/win/AdbWinUsbApi.dll', bin_folder)
                common.download('http://cnbj1-fds.api.xiaomi.net/global.ci/others/adb/win/adb.exe', bin_folder)
                _adb = os.path.join(bin_folder, 'adb.exe')
            elif sys.platform == "darwin":
                common.download('http://cnbj1-fds.api.xiaomi.net/global.ci/others/adb/mac/adb', bin_folder)
                _adb = os.path.join(bin_folder, 'adb')
            else:
                common.download('http://cnbj1-fds.api.xiaomi.net/global.ci/others/adb/linux/adb', bin_folder)
                _adb = os.path.join(bin_folder, 'adb')
            os.chmod(_adb, stat.S_IXUSR | stat.S_IXGRP | stat.S_IXOTH)
    return _adb


def run_adb(cmd, serial=None, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, stdin=None, debug=False):
    """
    Execute adb shell
    :param cmd: your adb command, command should be string when shell=True and list when shell=False.
        ex: root / pull / push
            ['ls /sdcard/']
    :param serial:
    :param shell: True if run as shell, False for otherwise
    :param stdout: execute output type
    :param stderr: execute error type
    :param stdin: execute input type
    :return: Process object
    """
    if shell:
        command = get_adb()
        if serial:
            command += " -s " + serial
        command += " " + cmd
    else:
        command = [get_adb()]
        if serial:
            command.append('-s')
            command.append(serial)
        if isinstance(cmd, list) or isinstance(cmd, tuple):
            command.extend(cmd)
        else:
            command.append(cmd)
    if debug:
        print(command)
    return subprocess.Popen(command, shell=shell, stdout=stdout, stderr=stderr, stdin=stdin)


def adb_shell(serial, shell_cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, stdin=None, debug=False):
    """
    Execute adb shell
    :param serial:
    :param shell_cmd: your shell command, command should be string when shell=True and list when shell=False.
        ex: ls /sdcard/
            ['ls /sdcard/']
    :param shell: True if run as shell, False for otherwise
    :param stdout: execute output type
    :param stderr: execute error type
    :param stdin: execute input type
    :return: Process object
    """
    if shell:
        command = "{adb} -s {s} shell {cmd}".format(adb=get_adb(), s=serial, cmd=shell_cmd)
    else:
        command = [get_adb(), '-s', serial, 'shell']
        if isinstance(shell_cmd, list) or isinstance(shell_cmd, tuple):
            command.extend(shell_cmd)
        else:
            command.append(shell_cmd)
    if debug:
        print(command)
    return subprocess.Popen(command, shell=shell, stdout=stdout, stderr=stderr, stdin=stdin)


def adb_shell_output(serial, shell_cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, stdin=None, debug=False):
    """
    Execute adb shell and get output
    :param serial:
    :param shell_cmd: your shell command, command should be string when shell=True and list when shell=False.
        ex: ls /sdcard/
            ['ls /sdcard/']
    :param shell: True if run as shell, False for otherwise
    :param stdout: execute output type
    :param stderr: execute error type
    :param stdin: execute input type
    :return: tuple of (standard output, error output)
    """
    return adb_shell(serial, shell_cmd, shell=shell, stdout=stdout, stderr=stderr, stdin=stdin, debug=debug).communicate()


def get_gaid(serial):
    cmd = ' grep \'"adid_key"\' /data/data/com.google.android.gms/shared_prefs/adid_settings.xml'
    out, err = adb_shell_output(serial, cmd)
    matcher = re.search(b'<.*>(.+)<\/.*>', out)
    if matcher:
        return matcher.group(1)
    return None


def get_android_id(serial):
    cmd = 'settings get secure android_id'
    out, err = adb_shell_output(serial, cmd)
    out = out.strip()
    if out:
        return out
    else:
        return None


def get_imeis(serial):
    cmd = 'getprop | grep "\.imei"'
    imeis = set()
    out, err = adb_shell_output(serial, cmd, shell=False)
    for line in out.decode('utf-8').split(os.linesep):
        matcher = re.match('^\[(.*)\]: \[(.*)\]$', line)
        if matcher:
            grps = matcher.groups()
            imeis.add(grps[1])
    return list(imeis)


def is_user_experience_enabled(serial):
    cmd = 'settings get secure upload_log_pref'
    out, err = adb_shell_output(serial, cmd)
    for line in out.decode('utf-8').split(os.linesep):
        if line == '1':
            return True
    return False


def get_apk_version(serial, package_name):
    ret, err = adb_shell_output(serial, 'dumpsys package {pkg} | grep versionName'.format(pkg=package_name), shell=False)
    tmp = ret.decode('utf-8').split('versionName=')
    if len(tmp) <= 1:
        return 'Unknown'
    else:
        return tmp[1].strip()


def find_uuid(text):
    regex = '([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})'
    matcher = re.search(regex, text)
    if matcher:
        return matcher.group(0)
    return None


def get_connected_wifi(serial):
    cmd = 'dumpsys wifi | grep "mWifiInfo"'
    out, err = adb_shell_output(serial, cmd, shell=False)
    for line in out.decode('utf-8').split(os.linesep):
        if 'state: COMPLETED' in line:
            import re
            matcher = re.search(' SSID: (.*), BSSID', line)
            if matcher:
                name = matcher.group(1)
                if name.startswith('"') and name.endswith('"'):
                    name = name[1:-1]
                return name
            else:
                return "Unknown"
    return None


def get_wifi_proxy(serial, wifi_ssid):
    cmd = 'dumpsys wifi'
    out, err = adb_shell_output(serial, cmd)
    found = False
    for line in out.decode('utf-8').split(os.linesep):
        if found and line.startswith('Proxy settings: '):
            if line != 'Proxy settings: STATIC':
                return None
        if found and line.startswith('HTTP proxy: '):
            matcher = re.match('HTTP proxy: \[(.*)\] (\d+) .*', line)
            return matcher.group(1), int(matcher.group(2))
        if line.startswith('ID: '):
            if found:
                return None
            matcher = re.match('ID: \d+ SSID: "(.*)" PROVIDER-NAME.*', line)
            if matcher and matcher.group(1) == wifi_ssid:
                found = True


def clear_data(serial, package_name):
    return adb_shell_output(serial, 'pm clear {pkg}'.format(pkg=package_name))


def adb_root(serial):
    ret, err = run_adb('root', serial).communicate()
    return not (err or b'cannot' in ret)


def adb_remount(serial):
    run_adb('disable-verity', serial).communicate()
    ret, err = run_adb('remount', serial).communicate()
    return len(err) == 0


def wait_for_device_ready(serial):
    run_adb('wait-for-usb-device', serial).communicate()


def get_device():
    from past.builtins import raw_input
    print('')
    print('请将手机装置连上电脑')
    print('Please connect your mobile device to PC')
    wait_for_device_ready(None)
    ret, err = run_adb('devices -l', shell=True).communicate()
    devices = list()
    for line in ret.decode('utf-8').split(os.linesep):
        matcher = re.match('([a-zA-Z0-9]*)\s+device\s+.*product:([^ ]+)\s+.*', line)
        if matcher:
            devices.append((matcher.group(1), matcher.group(2)))
    if len(devices) == 1:
        serial = devices[0][0]
        product = devices[0][1]
        print('')
        print('侦测到装置"{} {}"，是否确认使用此装置?'.format(serial, product))
        print('Found device "{} {}", use this device to test?'.format(serial, product))
        ch = raw_input("(Y/n)")
        if ch == '' or ch.lower() == 'y':
            return serial

    while True:
        devices = list()
        ret, err = run_adb('devices -l', shell=True).communicate()
        for line in ret.decode('utf-8').split(os.linesep):
            matcher = re.match('([a-zA-Z0-9]*)\s{4,}\w+\s+[^ ]+\s+([^ ]+)\s+.*', line)
            if matcher:
                devices.append((matcher.group(1), matcher.group(2)))
        print('')
        print('Select device:')
        if len(devices) == 0:
            print('< No device connected >')
        else:
            for i in range(len(devices)):
                serial = devices[i][0]
                product = devices[i][1].replace("product:", "")
                print('({})  {}\t{}'.format(i+1, serial, product))
        ch = raw_input("Num:")
        try:
            return devices[int(ch)-1][0]
        except Exception:
            pass
